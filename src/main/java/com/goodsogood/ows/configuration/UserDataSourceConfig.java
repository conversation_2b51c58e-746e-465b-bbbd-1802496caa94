package com.goodsogood.ows.configuration;

import com.goodsogood.ows.dmconfig.StatementHandlerInterceptor;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * User数据源
 * <AUTHOR>
 * @date 2019/11/13
 */
@Configuration
@MapperScan(basePackages = "com.goodsogood.ows.mapper.user", sqlSessionFactoryRef = UserDataSourceConfig.SQL_SESSION_FACTORY)
public class UserDataSourceConfig {

    private static final String DATA_SOURCE = "userDataSource";

    private static final String TRANSACTION_MANAGER = "userTransactionManager";

    static final String SQL_SESSION_FACTORY = "userSqlSessionFactory";

    private static final String DATA_PREFIX = "spring.datasource.user";

    @Autowired
    private StatementHandlerInterceptor statementHandlerInterceptor;

    @Bean(name = DATA_SOURCE)
    @ConfigurationProperties(prefix = DATA_PREFIX)
    public DataSource UserDataSource(){
        return DataSourceBuilder.create().type(HikariDataSource.class).build();
    }

    @Bean(name = TRANSACTION_MANAGER)
    public DataSourceTransactionManager mysqlTransactionManager() {
        return new DataSourceTransactionManager(UserDataSource());
    }

    @Bean(name = SQL_SESSION_FACTORY)
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier(DATA_SOURCE) DataSource dataSource)
            throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);

        // 添加达梦数据库SQL转换拦截器
        sessionFactory.setPlugins(statementHandlerInterceptor);

        return sessionFactory.getObject();
    }
}
